/**
 * Springfield Town Builder - Main Game Class
 * Coordinates all game systems and manages game state
 */

class Game extends EventEmitter {
    constructor() {
        super();
        
        // Game state
        this.isInitialized = false;
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = null;
        
        // Core systems
        this.engine3D = null;
        this.cameraController = null;
        this.saveSystem = null;
        this.currencySystem = null;
        this.buildingSystem = null;
        this.characterSystem = null;
        this.taskSystem = null;
        this.multiplayerSystem = null;
        
        // UI managers
        this.uiManager = null;
        this.menuSystem = null;
        
        // Game settings
        this.settings = {
            soundEnabled: true,
            musicEnabled: true,
            notifications: true,
            autoSave: true
        };
        
        this.init();
    }
    
    async init() {
        console.log('Initializing Springfield Town Builder...');
        
        try {
            await this.initializeSystems();
            await this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('Game initialized successfully');
            
            this.emit('gameInitialized');
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.emit('gameInitializationFailed', error);
        }
    }
    
    async initializeSystems() {
        // Initialize 3D engine
        const canvas = document.getElementById('game-canvas');
        this.engine3D = new Engine3D(canvas);
        
        // Initialize camera controller
        this.cameraController = new CameraController(this.engine3D.getCamera(), canvas);
        
        // Initialize core systems
        this.saveSystem = new SaveSystem();
        this.currencySystem = new CurrencySystem();
        this.buildingSystem = new BuildingSystem(this.engine3D, this.currencySystem);
        this.characterSystem = new CharacterSystem(this.engine3D, this.buildingSystem, this.currencySystem);
        this.taskSystem = new TaskSystem(this.characterSystem, this.currencySystem);
        this.multiplayerSystem = new MultiplayerSystem();
        
        // Initialize UI managers
        this.uiManager = new UIManager(this);
        this.menuSystem = new MenuSystem(this);
        
        // Wait for systems to initialize
        await Promise.all([
            this.buildingSystem.init?.() || Promise.resolve(),
            this.characterSystem.init?.() || Promise.resolve(),
            this.taskSystem.init?.() || Promise.resolve(),
            this.multiplayerSystem.init?.() || Promise.resolve()
        ]);

        // Initialize UI components after systems are ready
        this.buildingMenu = new BuildingMenu(this.buildingSystem);
        this.taskPanel = new TaskPanel(this.characterSystem, this.taskSystem);

        this.buildingMenu.init();
        this.taskPanel.init();
    }
    
    setupEventListeners() {
        // System event listeners
        this.saveSystem.on('gameLoaded', (event) => this.onGameLoaded(event));
        this.saveSystem.on('gameSaved', (event) => this.onGameSaved(event));
        this.saveSystem.on('newGameCreated', (event) => this.onNewGameCreated(event));
        
        this.currencySystem.on('levelUp', (event) => this.onLevelUp(event));
        
        this.buildingSystem.on('buildingPlaced', (event) => this.onBuildingPlaced(event));
        this.buildingSystem.on('buildingSelected', (building) => this.onBuildingSelected(building));
        
        this.characterSystem.on('characterSelected', (character) => this.onCharacterSelected(character));
        this.characterSystem.on('taskCompleted', (event) => this.onTaskCompleted(event));
        
        // Engine update loop
        this.engine3D.on('update', (deltaTime) => this.update(deltaTime));
        
        // Window events
        window.addEventListener('beforeunload', () => this.onBeforeUnload());
    }
    
    // Game lifecycle methods
    async startGame(saveSlot = null) {
        if (!this.isInitialized) {
            console.error('Game not initialized');
            return false;
        }
        
        if (saveSlot !== null) {
            // Load existing game
            const saveData = await this.saveSystem.loadGame(saveSlot);
            if (!saveData) {
                console.error('Failed to load game from slot', saveSlot);
                return false;
            }
        } else {
            // Start new game with default state
            this.gameState = this.createDefaultGameState();
            this.applyGameState(this.gameState);
        }
        
        // Start game systems
        this.engine3D.start();
        this.isRunning = true;
        
        // Show game UI
        this.uiManager.showGameUI();
        
        console.log('Game started');
        this.emit('gameStarted');
        return true;
    }
    
    pauseGame() {
        if (!this.isRunning) return;
        
        this.isPaused = true;
        this.emit('gamePaused');
    }
    
    resumeGame() {
        if (!this.isRunning || !this.isPaused) return;
        
        this.isPaused = false;
        this.emit('gameResumed');
    }
    
    stopGame() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        this.isPaused = false;
        
        // Stop game systems
        this.engine3D.stop();
        
        // Hide game UI
        this.uiManager.hideGameUI();
        
        console.log('Game stopped');
        this.emit('gameStopped');
    }
    
    async saveGame(slotNumber = null) {
        if (!this.isRunning) return false;
        
        const currentSlot = slotNumber || this.saveSystem.getCurrentSlot();
        if (currentSlot === null) {
            console.error('No save slot specified');
            return false;
        }
        
        const gameState = this.getGameState();
        return await this.saveSystem.saveGame(currentSlot, gameState);
    }
    
    async newGame(slotNumber, townName = 'Springfield') {
        const defaultState = this.createDefaultGameState(townName);
        const success = await this.saveSystem.saveGame(slotNumber, defaultState);
        
        if (success) {
            this.gameState = defaultState;
            this.applyGameState(this.gameState);
            return true;
        }
        
        return false;
    }
    
    // Game state management
    createDefaultGameState(townName = 'Springfield') {
        return {
            townName: townName,
            level: 1,
            money: 1000,
            donuts: 50,
            xp: 0,
            
            buildings: [],
            characters: [],
            tasks: [],
            
            settings: { ...this.settings },
            
            stats: {
                timePlayed: 0,
                buildingsBuilt: 0,
                tasksCompleted: 0,
                donutsSpent: 0
            },
            
            friends: [],
            unlockedBuildings: ['simpson_house', 'kwik_e_mart'],
            unlockedCharacters: ['homer'],
            completedQuests: [],
            
            lastPlayed: new Date().toISOString()
        };
    }
    
    getGameState() {
        if (!this.isRunning) return null;
        
        return {
            townName: this.gameState?.townName || 'Springfield',
            level: this.currencySystem.getLevel(),
            money: this.currencySystem.getMoney(),
            donuts: this.currencySystem.getDonuts(),
            xp: this.currencySystem.getXP(),
            
            buildings: this.buildingSystem.getState().buildings,
            characters: this.characterSystem.getState().characters,
            tasks: this.taskSystem?.getState()?.tasks || [],
            
            settings: { ...this.settings },
            
            stats: this.gameState?.stats || {
                timePlayed: 0,
                buildingsBuilt: 0,
                tasksCompleted: 0,
                donutsSpent: 0
            },
            
            friends: this.gameState?.friends || [],
            unlockedBuildings: this.gameState?.unlockedBuildings || [],
            unlockedCharacters: this.gameState?.unlockedCharacters || [],
            completedQuests: this.gameState?.completedQuests || [],
            
            lastPlayed: new Date().toISOString()
        };
    }
    
    applyGameState(state) {
        this.gameState = state;

        // Apply currency state
        this.currencySystem.setState({
            money: state.money,
            donuts: state.donuts,
            xp: state.xp,
            level: state.level
        });

        // Apply building state
        this.buildingSystem.setState({ buildings: state.buildings });

        // Apply character state
        this.characterSystem.setState({ characters: state.characters });

        // Apply task state
        if (this.taskSystem && state.tasks) {
            this.taskSystem.setState({ tasks: state.tasks });
        }

        // Apply settings
        if (state.settings) {
            this.settings = { ...state.settings };
        }

        // If this is a new game with no buildings, place the starter building
        if (state.buildings.length === 0) {
            this.placeStarterBuildings();
        }

        console.log('Game state applied');
    }

    placeStarterBuildings() {
        // Place starter buildings and characters
        setTimeout(() => {
            if (this.buildingSystem.buildingData && this.buildingSystem.buildingData['simpson_house']) {
                // Place Simpson House at the center
                const housePosition = new THREE.Vector3(0, 0, 0);
                const house = this.buildingSystem.createBuilding(
                    this.buildingSystem.buildingData['simpson_house'],
                    housePosition
                );
                this.buildingSystem.placedBuildings.set(house.userData.id, house);

                // Place Kwik-E-Mart to get players started with income
                if (this.buildingSystem.buildingData['kwik_e_mart']) {
                    const kwikEMartPosition = new THREE.Vector3(6, 0, -4);
                    const kwikEMart = this.buildingSystem.createBuilding(
                        this.buildingSystem.buildingData['kwik_e_mart'],
                        kwikEMartPosition
                    );
                    this.buildingSystem.placedBuildings.set(kwikEMart.userData.id, kwikEMart);
                }

                // Create starter characters
                setTimeout(() => {
                    // Homer near the house
                    const homerPosition = new THREE.Vector3(2, 0, 2);
                    this.characterSystem.createCharacter('homer', homerPosition);

                    // Apu near Kwik-E-Mart
                    if (this.characterSystem.characterData['apu']) {
                        const apuPosition = new THREE.Vector3(6, 0, -2);
                        this.characterSystem.createCharacter('apu', apuPosition);
                    }
                }, 500);

                // Show welcome message with starter info
                setTimeout(() => {
                    this.showStarterWelcome();
                }, 2000);

                console.log('Starter buildings and characters placed');
            }
        }, 1000);
    }

    showStarterWelcome() {
        const welcome = document.createElement('div');
        welcome.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 215, 0, 0.95);
            border: 3px solid #FF6B35;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            color: #8B4513;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        welcome.innerHTML = `
            <h2>🍩 Welcome to Springfield! 🍩</h2>
            <p style="margin: 15px 0;">You've started with the <strong>Simpson House</strong> and <strong>Kwik-E-Mart</strong>!</p>
            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <p><strong>💡 Quick Tips:</strong></p>
                <p>• Click the 💰 Collect button to gather income</p>
                <p>• Use building categories (🏠 🏪 🎪 🌳) to build more</p>
                <p>• Check the Characters tab to assign tasks</p>
                <p>• Level up to unlock new content!</p>
            </div>
            <button onclick="this.parentElement.remove()"
                    style="background: #4CAF50; color: white; border: none;
                           padding: 12px 25px; border-radius: 15px; cursor: pointer;
                           font-size: 1.1em; font-weight: bold; margin-top: 10px;">
                Let's Build Springfield!
            </button>
        `;

        document.body.appendChild(welcome);
    }
    
    // Game loop
    update(deltaTime) {
        if (!this.isRunning || this.isPaused) return;
        
        // Update camera controller
        this.cameraController.update();
        
        // Update game statistics
        if (this.gameState?.stats) {
            this.gameState.stats.timePlayed += deltaTime;
        }
        
        // Update UI
        this.uiManager?.update(deltaTime);
    }
    
    // Event handlers
    onGameLoaded(event) {
        console.log('Game loaded from slot', event.slot);
        this.applyGameState(event.data);
        this.emit('gameStateChanged', event.data);
    }
    
    onGameSaved(event) {
        console.log('Game saved to slot', event.slot);
        this.emit('gameSaved', event);
    }
    
    onNewGameCreated(event) {
        console.log('New game created in slot', event.slot);
        this.applyGameState(event.data);
        this.emit('gameStateChanged', event.data);
    }
    
    onLevelUp(event) {
        console.log('Level up!', event);
        
        // Update statistics
        if (this.gameState?.stats) {
            // Level up might unlock new content
            this.checkUnlocks(event.newLevel);
        }
        
        this.emit('levelUp', event);
    }
    
    onBuildingPlaced(event) {
        console.log('Building placed:', event.type);
        
        // Update statistics
        if (this.gameState?.stats) {
            this.gameState.stats.buildingsBuilt++;
        }
        
        this.emit('buildingPlaced', event);
    }
    
    onBuildingSelected(building) {
        this.uiManager?.showBuildingInfo(building);
    }
    
    onCharacterSelected(character) {
        this.uiManager?.showCharacterInfo(character);
    }
    
    onTaskCompleted(event) {
        console.log('Task completed:', event.task.name);
        
        // Update statistics
        if (this.gameState?.stats) {
            this.gameState.stats.tasksCompleted++;
        }
        
        this.emit('taskCompleted', event);
    }
    
    onBeforeUnload() {
        // Auto-save before closing
        if (this.isRunning && this.settings.autoSave) {
            this.saveGame();
        }
    }
    
    // Utility methods
    checkUnlocks(level) {
        // Check for new building unlocks
        // Check for new character unlocks
        // This would be expanded based on game progression
    }
    
    // Public API methods
    getTownName() {
        return this.gameState?.townName || 'Springfield';
    }
    
    setTownName(name) {
        if (this.gameState) {
            this.gameState.townName = name;
        }
    }
    
    getSettings() {
        return { ...this.settings };
    }
    
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.emit('settingsChanged', this.settings);
    }
    
    // Cleanup
    dispose() {
        console.log('Disposing game...');
        
        this.stopGame();
        
        // Dispose systems
        this.engine3D?.dispose?.();
        this.cameraController?.dispose?.();
        this.saveSystem?.dispose?.();
        this.currencySystem?.dispose?.();
        this.buildingSystem?.dispose?.();
        this.characterSystem?.dispose?.();
        this.taskSystem?.dispose?.();
        this.multiplayerSystem?.dispose?.();
        this.uiManager?.dispose?.();
        this.menuSystem?.dispose?.();
        
        this.removeAllListeners();
    }
}

// Make Game available globally
window.Game = Game;
