const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        // icon: path.join(__dirname, '../../assets/icons/donut-icon.png'), // Commented out until proper icon is created
        title: 'Springfield Town Builder',
        show: false
    });

    // Load the index.html of the app
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // Open DevTools in development
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }

    // Emitted when the window is closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers for game functionality
ipcMain.handle('save-game', async (event, saveData, slotNumber) => {
    try {
        const savesDir = path.join(__dirname, '../../data/saves');
        if (!fs.existsSync(savesDir)) {
            fs.mkdirSync(savesDir, { recursive: true });
        }
        
        const saveFile = path.join(savesDir, `save_slot_${slotNumber}.json`);
        fs.writeFileSync(saveFile, JSON.stringify(saveData, null, 2));
        return { success: true };
    } catch (error) {
        console.error('Save error:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-game', async (event, slotNumber) => {
    try {
        const saveFile = path.join(__dirname, `../../data/saves/save_slot_${slotNumber}.json`);
        if (fs.existsSync(saveFile)) {
            const saveData = JSON.parse(fs.readFileSync(saveFile, 'utf8'));
            return { success: true, data: saveData };
        } else {
            return { success: false, error: 'Save file not found' };
        }
    } catch (error) {
        console.error('Load error:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('get-save-slots', async () => {
    try {
        const savesDir = path.join(__dirname, '../../data/saves');
        const slots = [];
        
        for (let i = 1; i <= 5; i++) {
            const saveFile = path.join(savesDir, `save_slot_${i}.json`);
            if (fs.existsSync(saveFile)) {
                const stats = fs.statSync(saveFile);
                const saveData = JSON.parse(fs.readFileSync(saveFile, 'utf8'));
                slots.push({
                    slot: i,
                    exists: true,
                    lastModified: stats.mtime,
                    townName: saveData.townName || `Springfield ${i}`,
                    level: saveData.level || 1,
                    donuts: saveData.donuts || 0
                });
            } else {
                slots.push({
                    slot: i,
                    exists: false
                });
            }
        }
        
        return { success: true, slots };
    } catch (error) {
        console.error('Get save slots error:', error);
        return { success: false, error: error.message };
    }
});

// Handle app protocol for multiplayer
app.setAsDefaultProtocolClient('springfield-town-builder');
