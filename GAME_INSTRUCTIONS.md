# Springfield Town Builder - Game Instructions

## 🎮 How to Run the Game

### Method 1: Browser Version (Easiest)
1. **Simple HTTP Server** (Recommended):
   ```bash
   node simple-server.js
   ```
   Then open: http://localhost:8080

2. **Direct File** (May have limitations):
   - Open `test.html` directly in your browser

### Method 2: Electron Desktop App
1. **Install dependencies** (first time only):
   ```bash
   npm install
   ```

2. **Run the game**:
   ```bash
   npm start
   ```
   
   Or use the provided scripts:
   - Windows: Double-click `run-game.bat`
   - PowerShell: `.\run-game.ps1`

## 🏗️ Game Features Overview

### 🏠 Building Your Springfield
- **Start with the Simpson House**: Your first building comes free
- **Build Kwik-E-Mart**: Unlocks Apu and generates income
- **Add Moe's Tavern**: Where <PERSON> can drink beer and socialize
- **Expand with more buildings**: School, Power Plant, Krusty Burger, etc.

### 👨‍👩‍👧‍👦 Character Management
- **<PERSON>**: Starts unlocked, can work at the power plant or drink at <PERSON>'s
- **Unlock more characters**: <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> Book Guy
- **Assign tasks**: Characters perform activities to earn money and XP
- **Watch them move**: Characters walk around town and animate while working

### 💰 Currency System
- **Money (💰)**: Earned from buildings and tasks, used to buy most items
- **Donuts (🍩)**: Premium currency, earned from leveling up or special events
- **XP (⭐)**: Experience points that increase your level and unlock content

### 🎯 Game Controls

#### Camera Controls
- **Mouse Drag**: Rotate camera around your town
- **Mouse Wheel**: Zoom in and out
- **WASD or Arrow Keys**: Pan the camera
- **Q/E Keys**: Zoom with keyboard

#### Building
1. **Select Category**: Click building category buttons (🏠 🏪 🎪 🌳)
2. **Choose Building**: Click a building from the menu (bottom-left)
3. **Place Building**: Click on the ground where you want to build
4. **Cancel**: Right-click to cancel placement

#### Character Interaction
- **Click Characters**: View info and current tasks
- **Click Buildings**: View building info and upgrade options
- **Assign Tasks**: Use the task panel (bottom-right) to manage activities

## 🎮 Gameplay Tips

### Getting Started
1. **Place your first building**: Start with the Simpson House (free)
2. **Build Kwik-E-Mart**: This unlocks Apu and provides steady income
3. **Assign Homer tasks**: Send him to work or drink beer for money/XP
4. **Collect income**: Click buildings with income indicators
5. **Level up**: Gain XP to unlock new buildings and characters

### Advanced Strategies
- **Upgrade buildings**: Spend money to increase income generation
- **Balance your economy**: Mix money-generating and donut-cost buildings
- **Complete character tasks**: Regular task completion provides steady income
- **Plan your layout**: Organize buildings for easy navigation and aesthetics

### Character Task Examples
- **Homer**: Drink beer at Moe's, work at power plant, eat donuts, take naps
- **Marge**: Grocery shopping, clean house, volunteer work
- **Bart**: Skateboard around town, make prank calls, serve detention
- **Lisa**: Play saxophone, study at library, environmental activism
- **Apu**: Work at Kwik-E-Mart, restock shelves, meditation

## 🏢 Building Categories

### 🏠 Residential
- **Simpson House**: The iconic family home
- **Flanders House**: Ned's perfectly maintained home

### 🏪 Commercial
- **Kwik-E-Mart**: Apu's convenience store
- **Moe's Tavern**: Springfield's favorite dive bar
- **Krusty Burger**: Fast food restaurant
- **The Android's Dungeon**: Comic Book Guy's store

### 🏛️ Community
- **Springfield Elementary**: Where Bart and Lisa go to school
- **Town Hall**: Seat of Springfield's government
- **Springfield Library**: Quiet place for learning

### 🏭 Industrial
- **Nuclear Power Plant**: Mr. Burns' facility where Homer works

## 💾 Save System
- **5 Save Slots**: Create multiple towns without login
- **Auto-Save**: Game saves automatically every 30 seconds
- **Manual Save**: Use the menu to save anytime
- **Load Games**: Select from existing save slots

## 🌐 Multiplayer (Coming Soon)
- Friends system
- Real-time collaborative building
- Trading resources
- Competitions and events
- Live broadcasting

## 🔧 Troubleshooting

### Game Won't Load
1. Make sure Three.js is loaded (check browser console)
2. Try the HTTP server method instead of direct file opening
3. Clear browser cache and reload

### Performance Issues
1. Close other browser tabs
2. Lower graphics quality in settings (when available)
3. Restart the game

### Building Issues
1. Make sure you have enough money/donuts
2. Check if the building is unlocked (level requirement)
3. Try placing in a different location

## 🎨 Authentic Simpsons Experience
This game captures the spirit of The Simpsons with:
- **Authentic character personalities**: Each character behaves true to the show
- **Iconic Springfield locations**: Faithful recreations of famous buildings
- **Show-accurate tasks**: Activities based on actual episodes
- **Simpsons humor**: References and jokes from the series

## 📝 Development Notes
- Built with Electron for desktop compatibility
- Uses Three.js for 3D graphics
- Save data stored locally (no internet required)
- Modular design for easy expansion

---

**D'oh!** Have fun building your Springfield! 🍩

*This is a fan project inspired by The Simpsons: Tapped Out. All Simpsons characters and references are property of Fox Broadcasting Company.*
