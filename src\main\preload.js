/**
 * Springfield Town Builder - Preload Script
 * Exposes safe Electron APIs to the renderer process
 */

const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    
    // Save/Load operations
    saveGame: (saveData, slotNumber) => ipcRenderer.invoke('save-game', saveData, slotNumber),
    loadGame: (slotNumber) => ipcRenderer.invoke('load-game', slotNumber),
    getSaveSlots: () => ipcRenderer.invoke('get-save-slots'),
    
    // Window operations
    closeWindow: () => ipcRenderer.invoke('close-window'),
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    
    // Platform info
    platform: process.platform,
    
    // Version info
    versions: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron
    }
});

console.log('Preload script loaded successfully');
