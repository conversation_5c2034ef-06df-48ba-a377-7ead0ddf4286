/**
 * Springfield Town Builder - Task Panel
 * Handles the task management interface
 */

class TaskPanel extends EventEmitter {
    constructor(characterSystem, taskSystem) {
        super();
        this.characterSystem = characterSystem;
        this.taskSystem = taskSystem;
        
        this.activeTasks = new Map();
        this.isVisible = true;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateTaskList();
        this.updateCharacterList();
        console.log('Task Panel initialized');
    }
    
    setupEventListeners() {
        // Tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // Task buttons
        const collectAllBtn = document.getElementById('collect-all-btn');
        const assignTasksBtn = document.getElementById('assign-tasks-btn');
        const unlockCharacterBtn = document.getElementById('unlock-character-btn');

        if (collectAllBtn) {
            collectAllBtn.addEventListener('click', () => this.collectAllTasks());
        }

        if (assignTasksBtn) {
            assignTasksBtn.addEventListener('click', () => this.assignRandomTasks());
        }

        if (unlockCharacterBtn) {
            unlockCharacterBtn.addEventListener('click', () => this.showCharacterUnlockMenu());
        }

        // Listen for character system events
        this.characterSystem.on('taskStarted', () => this.updateTaskList());
        this.characterSystem.on('taskCompleted', () => this.updateTaskList());
        this.characterSystem.on('characterCreated', () => {
            this.updateTaskList();
            this.updateCharacterList();
        });
    }
    
    updateTaskList() {
        const container = document.getElementById('task-list');
        const countElement = document.getElementById('task-count');
        
        if (!container) return;
        
        container.innerHTML = '';
        let taskCount = 0;
        
        // Get all active characters and their tasks
        for (const [id, character] of this.characterSystem.activeCharacters) {
            if (character.userData.currentTask) {
                const taskElement = this.createTaskElement(character);
                container.appendChild(taskElement);
                taskCount++;
            }
        }
        
        // Update task count
        if (countElement) {
            countElement.textContent = taskCount.toString();
        }
        
        // Show message if no tasks
        if (taskCount === 0) {
            const noTasksElement = document.createElement('div');
            noTasksElement.className = 'no-tasks-message';
            noTasksElement.textContent = 'No active tasks. Click "Assign Tasks" to get started!';
            noTasksElement.style.cssText = `
                text-align: center;
                color: #666;
                font-style: italic;
                padding: 20px;
            `;
            container.appendChild(noTasksElement);
        }
    }
    
    createTaskElement(character) {
        const task = character.userData.currentTask;
        const element = document.createElement('div');
        element.className = 'task-item';
        
        // Calculate progress
        const startTime = character.userData.lastTaskTime;
        const duration = task.duration * 1000;
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const isCompleted = progress >= 1;
        
        if (isCompleted) {
            element.classList.add('completed');
        }
        
        element.innerHTML = `
            <img src="${character.userData.avatar || 'assets/characters/default.png'}" 
                 alt="${character.userData.name}" 
                 class="task-character-avatar"
                 onerror="this.style.display='none'">
            <div class="task-info">
                <div class="task-character-name">${character.userData.name}</div>
                <div class="task-description">${task.name}</div>
            </div>
            <div class="task-progress">
                <div class="task-time">
                    ${isCompleted ? 'Complete!' : this.formatTimeRemaining(duration - elapsed)}
                </div>
                <div class="task-reward">
                    +${task.reward.money || 0} 💰 +${task.reward.xp || 0} ⭐
                </div>
            </div>
        `;
        
        // Add click handler for completed tasks
        if (isCompleted) {
            element.style.cursor = 'pointer';
            element.addEventListener('click', () => {
                this.collectTask(character);
            });
        }
        
        return element;
    }
    
    formatTimeRemaining(milliseconds) {
        if (milliseconds <= 0) return 'Complete!';
        
        const seconds = Math.ceil(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    collectTask(character) {
        // This would normally be handled by the character system
        // For now, just update the display
        this.updateTaskList();
    }
    
    collectAllTasks() {
        // Collect all completed tasks
        for (const [id, character] of this.characterSystem.activeCharacters) {
            if (character.userData.currentTask) {
                const startTime = character.userData.lastTaskTime;
                const duration = character.userData.currentTask.duration * 1000;
                const elapsed = Date.now() - startTime;
                
                if (elapsed >= duration) {
                    this.collectTask(character);
                }
            }
        }
    }
    
    assignRandomTasks() {
        console.log('Assign random tasks clicked');
        console.log('Active characters:', this.characterSystem.activeCharacters);

        let tasksAssigned = 0;

        // Assign random tasks to idle characters
        for (const [id, character] of this.characterSystem.activeCharacters) {
            console.log('Checking character:', character.userData.name, 'current task:', character.userData.currentTask);

            if (!character.userData.currentTask) {
                console.log('Assigning task to:', character.userData.name);
                this.characterSystem.assignRandomTask(character);
                tasksAssigned++;
            }
        }

        console.log('Tasks assigned:', tasksAssigned);

        if (tasksAssigned === 0) {
            alert('All characters are already busy or no characters available!');
        } else {
            alert(`Assigned tasks to ${tasksAssigned} character(s)!`);
        }

        this.updateTaskList();
    }
    
    switchTab(tabName) {
        // Update tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Update tab content
        const tasksTab = document.getElementById('tasks-tab');
        const charactersTab = document.getElementById('characters-tab');

        if (tabName === 'tasks') {
            tasksTab.classList.remove('hidden');
            charactersTab.classList.add('hidden');
        } else if (tabName === 'characters') {
            tasksTab.classList.add('hidden');
            charactersTab.classList.remove('hidden');
            this.updateCharacterList();
        }
    }

    updateCharacterList() {
        const container = document.getElementById('character-list');
        const countElement = document.getElementById('character-count');

        if (!container) return;

        container.innerHTML = '';

        // Get available characters
        const availableCharacters = this.getAvailableCharacters();
        const activeCharacters = Array.from(this.characterSystem.activeCharacters.values());

        // Update character count
        if (countElement) {
            countElement.textContent = activeCharacters.length.toString();
        }

        // Show active characters
        activeCharacters.forEach(character => {
            const characterElement = this.createCharacterElement(character, true);
            container.appendChild(characterElement);
        });

        // Show unlockable characters
        availableCharacters.forEach(characterData => {
            if (!this.isCharacterActive(characterData.id)) {
                const characterElement = this.createCharacterElement(characterData, false);
                container.appendChild(characterElement);
            }
        });

        // Show message if no characters
        if (activeCharacters.length === 0 && availableCharacters.length === 0) {
            const noCharactersElement = document.createElement('div');
            noCharactersElement.className = 'no-characters-message';
            noCharactersElement.textContent = 'No characters available yet!';
            noCharactersElement.style.cssText = `
                text-align: center;
                color: #666;
                font-style: italic;
                padding: 20px;
            `;
            container.appendChild(noCharactersElement);
        }
    }

    getAvailableCharacters() {
        if (!this.characterSystem.characterData) return [];

        return Object.values(this.characterSystem.characterData).filter(character => {
            // Check if character is unlocked by level
            const currentLevel = window.game?.currencySystem?.getLevel() || 1;
            return currentLevel >= (character.unlockLevel || 1);
        });
    }

    isCharacterActive(characterType) {
        for (const [id, character] of this.characterSystem.activeCharacters) {
            if (character.userData.type === characterType) {
                return true;
            }
        }
        return false;
    }

    createCharacterElement(characterData, isActive) {
        const element = document.createElement('div');
        element.className = `character-item ${isActive ? '' : 'locked'}`;

        // Character emoji based on type
        const characterEmojis = {
            'homer': '👨',
            'marge': '👩',
            'bart': '👦',
            'lisa': '👧',
            'apu': '👳‍♂️',
            'moe': '👨‍🦲',
            'comic_book_guy': '🤓'
        };

        const emoji = characterEmojis[characterData.id || characterData.type] || '👤';
        const name = characterData.name || characterData.userData?.name || 'Unknown';
        const status = isActive ?
            (characterData.userData?.currentTask ? `Busy: ${characterData.userData.currentTask.name}` : 'Available') :
            'Not unlocked';

        let costText = '';
        if (!isActive && characterData.cost) {
            if (characterData.cost.type === 'free') {
                costText = 'FREE';
            } else if (characterData.cost.type === 'money') {
                costText = `$${characterData.cost.amount}`;
            } else if (characterData.cost.type === 'donuts') {
                costText = `${characterData.cost.amount} 🍩`;
            }
        }

        element.innerHTML = `
            <div class="character-avatar">${emoji}</div>
            <div class="character-info">
                <div class="character-name">${name}</div>
                <div class="character-status">${status}</div>
            </div>
            <div class="character-cost ${characterData.cost?.type === 'donuts' ? 'donut-cost' : ''}">${costText}</div>
        `;

        // Add click handler for unlocking characters
        if (!isActive && characterData.cost) {
            element.style.cursor = 'pointer';
            element.addEventListener('click', () => {
                this.unlockCharacter(characterData);
            });
        }

        return element;
    }

    unlockCharacter(characterData) {
        if (!characterData.cost) return;

        const currencySystem = window.game?.currencySystem;
        if (!currencySystem) return;

        // Check if player can afford the character
        let canAfford = false;
        if (characterData.cost.type === 'free') {
            canAfford = true;
        } else if (characterData.cost.type === 'money') {
            canAfford = currencySystem.canAfford(characterData.cost.amount);
        } else if (characterData.cost.type === 'donuts') {
            canAfford = currencySystem.canAffordDonuts(characterData.cost.amount);
        }

        if (!canAfford) {
            alert(`Not enough ${characterData.cost.type === 'donuts' ? 'donuts' : 'money'} to unlock ${characterData.name}!`);
            return;
        }

        // Deduct cost
        if (characterData.cost.type === 'money') {
            currencySystem.spendMoney(characterData.cost.amount, `unlock_${characterData.id}`);
        } else if (characterData.cost.type === 'donuts') {
            currencySystem.spendDonuts(characterData.cost.amount, `unlock_${characterData.id}`);
        }

        // Create character near the Simpson House or at a random location
        const position = new THREE.Vector3(
            (Math.random() - 0.5) * 10,
            0,
            (Math.random() - 0.5) * 10
        );

        this.characterSystem.createCharacter(characterData.id, position);

        // Show success message
        this.showCharacterUnlockedMessage(characterData);
    }

    showCharacterUnlockedMessage(characterData) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #8B4513;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">🎉</span>
                <div>
                    <div>${characterData.name} unlocked!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">Welcome to Springfield!</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    showCharacterUnlockMenu() {
        alert('Click on a locked character to unlock them!');
    }

    dispose() {
        this.removeAllListeners();
    }
}
