<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Springfield Town Builder - Browser Test</title>
    <link rel="stylesheet" href="src/renderer/css/main.css">
    <link rel="stylesheet" href="src/renderer/css/ui.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-donut" style="font-size: 4em;">🍩</div>
            <h1>Springfield Town Builder</h1>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
            <p id="loading-text">Loading Springfield...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="main-menu" class="main-menu hidden">
        <div class="menu-background">
            <div class="menu-content">
                <h1 class="game-title">
                    <span class="title-donut" style="font-size: 1em;">🍩</span>
                    Springfield Town Builder
                </h1>
                <div class="menu-buttons">
                    <button id="new-game-btn" class="menu-button">New Game</button>
                    <button id="load-game-btn" class="menu-button">Load Game</button>
                    <button id="multiplayer-btn" class="menu-button">Multiplayer</button>
                    <button id="settings-btn" class="menu-button">Settings</button>
                    <button id="exit-btn" class="menu-button">Exit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Slot Selection -->
    <div id="save-slot-menu" class="save-slot-menu hidden">
        <div class="menu-background">
            <div class="menu-content">
                <h2>Select Save Slot</h2>
                <div class="save-slots" id="save-slots">
                    <!-- Save slots will be populated dynamically -->
                </div>
                <button id="back-to-menu-btn" class="menu-button">Back to Menu</button>
            </div>
        </div>
    </div>

    <!-- Game Container -->
    <div id="game-container" class="game-container hidden">
        <!-- 3D Game Canvas -->
        <canvas id="game-canvas" class="game-canvas"></canvas>
        
        <!-- UI Overlay -->
        <div class="ui-overlay">
            <!-- Top UI Bar -->
            <div class="top-ui-bar">
                <div class="currency-display">
                    <div class="currency-item">
                        <span class="currency-icon">💰</span>
                        <span id="money-amount">1000</span>
                    </div>
                    <div class="currency-item donut-currency">
                        <span class="currency-icon">🍩</span>
                        <span id="donut-amount">50</span>
                    </div>
                    <div class="currency-item">
                        <span class="currency-icon">⭐</span>
                        <span id="xp-amount">0</span>
                    </div>
                </div>
                <div class="town-info">
                    <h3 id="town-name">Springfield</h3>
                    <div class="level-display">Level <span id="town-level">1</span></div>
                </div>
                <div class="top-buttons">
                    <button id="collect-income-btn" class="ui-button" onclick="collectAllIncome()" style="background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); color: #8B4513;">💰 Collect</button>
                    <button id="menu-btn" class="ui-button">Menu</button>
                    <button id="help-btn" class="ui-button" onclick="showHelp()">Help</button>
                    <button id="test-tasks-btn" class="ui-button" onclick="testTasks()" style="background: #9C27B0; color: white;">Test Tasks</button>
                    <button id="store-btn" class="ui-button">Store</button>
                </div>
            </div>

            <!-- Building Menu (Bottom Left) -->
            <div class="building-menu">
                <div class="building-categories">
                    <button class="category-btn active" data-category="residential">🏠</button>
                    <button class="category-btn" data-category="commercial">🏪</button>
                    <button class="category-btn" data-category="entertainment">🎪</button>
                    <button class="category-btn" data-category="decorations">🌳</button>
                </div>
                <div class="building-buttons" id="building-buttons">
                    <!-- Building buttons will be populated dynamically -->
                </div>
            </div>

            <!-- Task Panel (Bottom Right) -->
            <div class="task-panel">
                <div class="panel-tabs">
                    <button class="tab-btn active" data-tab="tasks">Tasks</button>
                    <button class="tab-btn" data-tab="characters">Characters</button>
                </div>

                <!-- Tasks Tab -->
                <div class="tab-content" id="tasks-tab">
                    <div class="task-header">
                        <h4>Tasks</h4>
                        <span class="task-count" id="task-count">0</span>
                    </div>
                    <div class="task-list" id="task-list">
                        <!-- Tasks will be populated dynamically -->
                    </div>
                    <div class="task-buttons">
                        <button id="collect-all-btn" class="task-button">Collect All</button>
                        <button id="assign-tasks-btn" class="task-button">Assign Tasks</button>
                    </div>
                </div>

                <!-- Characters Tab -->
                <div class="tab-content hidden" id="characters-tab">
                    <div class="task-header">
                        <h4>Characters</h4>
                        <span class="character-count" id="character-count">0</span>
                    </div>
                    <div class="character-list" id="character-list">
                        <!-- Characters will be populated dynamically -->
                    </div>
                    <div class="task-buttons">
                        <button id="unlock-character-btn" class="task-button">Unlock Character</button>
                    </div>
                </div>
            </div>

            <!-- Character Panel -->
            <div class="character-panel hidden" id="character-panel">
                <div class="character-info">
                    <img id="character-avatar" src="" alt="Character">
                    <h4 id="character-name"></h4>
                    <p id="character-status"></p>
                </div>
                <div class="character-tasks" id="character-tasks">
                    <!-- Character-specific tasks -->
                </div>
                <button id="close-character-panel" class="ui-button">Close</button>
            </div>

            <!-- Building Info Panel -->
            <div class="building-info-panel hidden" id="building-info-panel">
                <div class="building-info">
                    <img id="building-image" src="" alt="Building">
                    <h4 id="building-name"></h4>
                    <p id="building-description"></p>
                </div>
                <div class="building-actions">
                    <button id="upgrade-building-btn" class="ui-button">Upgrade</button>
                    <button id="move-building-btn" class="ui-button">Move</button>
                    <button id="store-building-btn" class="ui-button">Store</button>
                </div>
                <button id="close-building-panel" class="ui-button">Close</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Three.js Local -->
    <script src="assets/js/three.min.js"></script>
    
    <!-- Game Scripts -->
    <script src="src/renderer/js/utils/EventEmitter.js"></script>
    <script src="src/renderer/js/engine/Engine3D.js"></script>
    <script src="src/renderer/js/engine/SceneManager.js"></script>
    <script src="src/renderer/js/engine/CameraController.js"></script>
    <script src="src/renderer/js/systems/SaveSystem.js"></script>
    <script src="src/renderer/js/systems/CurrencySystem.js"></script>
    <script src="src/renderer/js/systems/BuildingSystem.js"></script>
    <script src="src/renderer/js/systems/CharacterSystem.js"></script>
    <script src="src/renderer/js/systems/TaskSystem.js"></script>
    <script src="src/renderer/js/systems/MultiplayerSystem.js"></script>
    <script src="src/renderer/js/ui/UIManager.js"></script>
    <script src="src/renderer/js/ui/BuildingMenu.js"></script>
    <script src="src/renderer/js/ui/TaskPanel.js"></script>
    <script src="src/renderer/js/ui/MenuSystem.js"></script>
    <script src="src/renderer/js/Game.js"></script>
    <script src="src/renderer/js/main.js"></script>

    <script>
        // Collect all income function
        function collectAllIncome() {
            if (window.game && window.game.buildingSystem) {
                let totalCollected = 0;

                for (const [id, building] of window.game.buildingSystem.placedBuildings) {
                    const collected = window.game.buildingSystem.collectIncome(building);
                    totalCollected += collected;
                }

                if (totalCollected > 0) {
                    // Show collection notification
                    const notification = document.createElement('div');
                    notification.style.cssText = \`
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
                        color: white;
                        padding: 20px 30px;
                        border-radius: 15px;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                        z-index: 1000;
                        font-family: 'Comic Sans MS', cursive, sans-serif;
                        font-weight: bold;
                        font-size: 1.2em;
                        animation: collectPulse 0.6s ease-out;
                    \`;

                    notification.innerHTML = \`
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <span style="font-size: 2em;">💰</span>
                            <div>
                                <div>Income Collected!</div>
                                <div style="font-size: 0.8em; opacity: 0.9;">+$\${totalCollected}</div>
                            </div>
                        </div>
                    \`;

                    document.body.appendChild(notification);

                    // Auto-remove after 2 seconds
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 2000);
                } else {
                    // No income to collect
                    const notification = document.createElement('div');
                    notification.style.cssText = \`
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(255, 193, 7, 0.9);
                        color: #8B4513;
                        padding: 15px 20px;
                        border-radius: 10px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                        z-index: 1000;
                        font-family: 'Comic Sans MS', cursive, sans-serif;
                        font-weight: bold;
                    \`;

                    notification.textContent = 'No income ready to collect yet!';
                    document.body.appendChild(notification);

                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 2000);
                }
            }
        }

        // Help function
        function showHelp() {
            const helpText = `
🍩 SPRINGFIELD TOWN BUILDER HELP 🍩

🏗️ BUILDING:
1. Click a category button (🏠 🏪 🎪 🌳) at bottom-left
2. Select a building from the menu
3. Click on the ground to place it
4. Right-click to cancel placement

💰 CURRENCY:
• Money (💰): Earned from buildings and tasks
• Donuts (🍩): Premium currency from leveling up
• XP (⭐): Experience points for progression

🎮 CONTROLS (Top-Down View):
• Mouse Drag: Rotate camera slightly
• Mouse Wheel: Zoom in/out
• WASD/Arrow Keys: Pan around town
• Q/E Keys: Quick zoom
• Click Buildings/Characters: View info

🎯 GETTING STARTED:
1. Start a new game (creates Simpson House)
2. Build Kwik-E-Mart for steady income
3. Assign character tasks for money/XP
4. Level up to unlock more content!

Have fun building your Springfield! 🏠
            `;

            alert(helpText);
        }

        // Test function to manually populate buildings
        function testBuildings() {
            console.log('Testing buildings...');
            if (window.game && window.game.buildingSystem) {
                console.log('Building system:', window.game.buildingSystem);
                console.log('Building data:', window.game.buildingSystem.buildingData);
                console.log('Building menu:', window.game.buildingMenu);

                if (window.game.buildingMenu) {
                    console.log('Populating building menu...');
                    window.game.buildingMenu.populateBuildingButtons();
                } else {
                    console.log('Building menu not found');
                }

                // Test category buttons
                const categoryButtons = document.querySelectorAll('.category-btn');
                console.log('Category buttons found:', categoryButtons.length);

                // Test building buttons container
                const container = document.getElementById('building-buttons');
                console.log('Building buttons container:', container);
                if (container) {
                    console.log('Container content:', container.innerHTML);
                }
            } else {
                console.log('Game or building system not found');
            }
        }

        // Test task assignment
        function testTasks() {
            console.log('Testing tasks...');
            if (window.game && window.game.characterSystem) {
                console.log('Character system:', window.game.characterSystem);
                console.log('Active characters:', window.game.characterSystem.activeCharacters);

                // Try to assign a random task to the first character
                const characters = Array.from(window.game.characterSystem.activeCharacters.values());
                if (characters.length > 0) {
                    const character = characters[0];
                    console.log('Assigning task to:', character.userData.name);
                    window.game.characterSystem.assignRandomTask(character);
                } else {
                    console.log('No characters found');
                }
            }
        }

        // Auto-test after 5 seconds
        setTimeout(() => {
            console.log('Auto-testing buildings...');
            testBuildings();
        }, 5000);

        // Force building menu population after 6 seconds
        setTimeout(() => {
            if (window.game && window.game.buildingMenu) {
                console.log('Force populating building menu...');
                window.game.buildingMenu.populateBuildingButtons();
            }
        }, 6000);

        // Show welcome message
        setTimeout(() => {
            if (window.game && window.game.isRunning) {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 215, 0, 0.95);
                    border: 3px solid #FF6B35;
                    border-radius: 20px;
                    padding: 30px;
                    text-align: center;
                    z-index: 1000;
                    font-family: 'Comic Sans MS', cursive, sans-serif;
                    color: #8B4513;
                    max-width: 400px;
                `;

                welcome.innerHTML = \`
                    <h2>🍩 Welcome to Springfield! 🍩</h2>
                    <p>Click the building categories (🏠 🏪 🎪 🌳) at the bottom-left to start building!</p>
                    <p>Need help? Click the "Help" button in the top-right.</p>
                    <button onclick="this.parentElement.remove()"
                            style="background: #4CAF50; color: white; border: none;
                                   padding: 10px 20px; border-radius: 15px; cursor: pointer; margin-top: 15px;">
                        Let's Build!
                    </button>
                \`;

                document.body.appendChild(welcome);
            }
        }, 8000);
    </script>
</body>
</html>
