{"characters": {"homer": {"id": "homer", "name": "<PERSON>", "description": "Nuclear safety inspector and beer enthusiast", "avatar": "assets/characters/homer.png", "model": "assets/models/characters/homer.glb", "unlockLevel": 1, "cost": {"type": "free"}, "category": "simpson_family", "personality": "lazy", "walkSpeed": 1.0, "tasks": [{"id": "drink_beer", "name": "Drink Beer at Moe's", "description": "<PERSON> enjoys a cold Duff at Moe's Tavern", "duration": 300, "reward": {"money": 50, "xp": 10}, "building": "moes_tavern", "animation": "drinking"}, {"id": "work_power_plant", "name": "Work at Power Plant", "description": "<PERSON> pretends to work at the nuclear plant", "duration": 600, "reward": {"money": 100, "xp": 20}, "building": "nuclear_power_plant", "animation": "working"}, {"id": "eat_donuts", "name": "Eat Donuts", "description": "<PERSON> indulges in his favorite treat", "duration": 180, "reward": {"money": 30, "xp": 8}, "building": "simpson_house", "animation": "eating"}, {"id": "sleep", "name": "Take a Nap", "description": "<PERSON> takes a well-deserved nap", "duration": 240, "reward": {"money": 25, "xp": 5}, "building": "simpson_house", "animation": "sleeping"}]}, "marge": {"id": "marge", "name": "<PERSON><PERSON>", "description": "Loving mother and voice of reason", "avatar": "assets/characters/marge.png", "model": "assets/models/characters/marge.glb", "unlockLevel": 3, "cost": {"type": "money", "amount": 2500}, "category": "simpson_family", "personality": "caring", "walkSpeed": 1.2, "tasks": [{"id": "grocery_shopping", "name": "Grocery Shopping", "description": "Marge shops for the family at Kwik-E-Mart", "duration": 360, "reward": {"money": 75, "xp": 15}, "building": "kwik_e_mart", "animation": "shopping"}, {"id": "clean_house", "name": "Clean House", "description": "<PERSON><PERSON> keeps the Simpson home tidy", "duration": 300, "reward": {"money": 60, "xp": 12}, "building": "simpson_house", "animation": "cleaning"}, {"id": "volunteer_work", "name": "Volunteer Work", "description": "<PERSON><PERSON> helps out at the community center", "duration": 480, "reward": {"money": 40, "xp": 25}, "building": "town_hall", "animation": "volunteering"}]}, "bart": {"id": "bart", "name": "<PERSON>", "description": "Mischievous troublemaker with a heart of gold", "avatar": "assets/characters/bart.png", "model": "assets/models/characters/bart.glb", "unlockLevel": 2, "cost": {"type": "money", "amount": 1500}, "category": "simpson_family", "personality": "mischievous", "walkSpeed": 1.5, "tasks": [{"id": "skateboard", "name": "Skateboard Around Town", "description": "<PERSON> cruises around Springfield on his skateboard", "duration": 240, "reward": {"money": 35, "xp": 10}, "building": null, "animation": "skateboarding"}, {"id": "prank_calls", "name": "Make Prank Calls", "description": "<PERSON> calls Moe's Tavern with silly names", "duration": 180, "reward": {"money": 25, "xp": 8}, "building": "simpson_house", "animation": "phone_pranking"}, {"id": "detention", "name": "<PERSON><PERSON>", "description": "<PERSON> writes lines on the chalkboard", "duration": 420, "reward": {"money": 20, "xp": 15}, "building": "springfield_elementary", "animation": "writing"}]}, "lisa": {"id": "lisa", "name": "<PERSON>", "description": "Brilliant student and saxophone player", "avatar": "assets/characters/lisa.png", "model": "assets/models/characters/lisa.glb", "unlockLevel": 4, "cost": {"type": "money", "amount": 3000}, "category": "simpson_family", "personality": "intelligent", "walkSpeed": 1.1, "tasks": [{"id": "play_saxophone", "name": "Play Saxophone", "description": "<PERSON> practices her beautiful saxophone music", "duration": 300, "reward": {"money": 80, "xp": 18}, "building": "simpson_house", "animation": "playing_sax"}, {"id": "study", "name": "Study at Library", "description": "<PERSON> hits the books at the library", "duration": 360, "reward": {"money": 45, "xp": 25}, "building": "springfield_library", "animation": "studying"}, {"id": "environmental_activism", "name": "Environmental Activism", "description": "<PERSON> fights for environmental causes", "duration": 480, "reward": {"money": 30, "xp": 30}, "building": "town_hall", "animation": "protesting"}]}, "apu": {"id": "apu", "name": "A<PERSON>emapetilon", "description": "Hardworking Kwik-E-Mart owner", "avatar": "assets/characters/apu.png", "model": "assets/models/characters/apu.glb", "unlockLevel": 5, "cost": {"type": "money", "amount": 4000}, "category": "townspeople", "personality": "hardworking", "walkSpeed": 1.0, "tasks": [{"id": "work_kwik_e_mart", "name": "Work at Kwik-E-Mart", "description": "<PERSON><PERSON> manages his convenience store", "duration": 480, "reward": {"money": 120, "xp": 20}, "building": "kwik_e_mart", "animation": "cashier"}, {"id": "restock_shelves", "name": "Restock Shelves", "description": "Apu restocks the Kwik-E-Mart inventory", "duration": 300, "reward": {"money": 80, "xp": 15}, "building": "kwik_e_mart", "animation": "stocking"}, {"id": "meditation", "name": "Meditation", "description": "<PERSON><PERSON> finds inner peace through meditation", "duration": 240, "reward": {"money": 40, "xp": 20}, "building": "simpson_house", "animation": "meditating"}]}, "moe": {"id": "moe", "name": "<PERSON>", "description": "Gruff tavern owner with a heart of gold", "avatar": "assets/characters/moe.png", "model": "assets/models/characters/moe.glb", "unlockLevel": 6, "cost": {"type": "donuts", "amount": 25}, "category": "townspeople", "personality": "gruff", "walkSpeed": 0.9, "tasks": [{"id": "tend_bar", "name": "Tend Bar", "description": "<PERSON> serves drinks at his tavern", "duration": 600, "reward": {"money": 150, "xp": 25}, "building": "moes_tavern", "animation": "bartending"}, {"id": "clean_glasses", "name": "Clean Glasses", "description": "<PERSON> cleans beer glasses with a dirty rag", "duration": 180, "reward": {"money": 50, "xp": 10}, "building": "moes_tavern", "animation": "cleaning"}, {"id": "ugly_contest", "name": "Enter Ugly Contest", "description": "<PERSON> competes in Springfield's ugly contest", "duration": 360, "reward": {"money": 100, "xp": 20}, "building": "town_hall", "animation": "posing"}]}, "comic_book_guy": {"id": "comic_book_guy", "name": "Comic Book Guy", "description": "Sarcastic comic book store owner", "avatar": "assets/characters/comic_book_guy.png", "model": "assets/models/characters/comic_book_guy.glb", "unlockLevel": 8, "cost": {"type": "donuts", "amount": 30}, "category": "townspeople", "personality": "sarcastic", "walkSpeed": 0.7, "tasks": [{"id": "read_comics", "name": "Read Comics", "description": "Comic Book Guy reads the latest issues", "duration": 300, "reward": {"money": 70, "xp": 15}, "building": "androids_dungeon", "animation": "reading"}, {"id": "organize_inventory", "name": "Organize Inventory", "description": "Comic Book Guy catalogs his collection", "duration": 480, "reward": {"money": 90, "xp": 20}, "building": "androids_dungeon", "animation": "organizing"}, {"id": "internet_trolling", "name": "Internet Trolling", "description": "Comic Book Guy argues online about comics", "duration": 240, "reward": {"money": 60, "xp": 12}, "building": "androids_dungeon", "animation": "typing"}]}}}