/* Springfield Town Builder - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    overflow: hidden;
    user-select: none;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #8B4513;
}

.loading-donut {
    width: 80px;
    height: 80px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255,255,255,0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px auto;
    border: 2px solid #8B4513;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 8px;
}

/* Main Menu */
.main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../assets/backgrounds/springfield-bg.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 900;
}

.menu-background {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 3px solid #FFD700;
}

.menu-content {
    text-align: center;
}

.game-title {
    font-size: 3em;
    color: #FF6B35;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-donut {
    width: 60px;
    height: 60px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-button {
    padding: 15px 30px;
    font-size: 1.2em;
    font-family: inherit;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: 3px solid #FF6B35;
    border-radius: 25px;
    color: #8B4513;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
    min-width: 200px;
}

.menu-button:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF6B35 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.menu-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Save Slot Menu */
.save-slot-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 950;
}

.save-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
    max-width: 800px;
}

.save-slot {
    background: linear-gradient(135deg, #E8F4FD 0%, #B3E5FC 100%);
    border: 3px solid #2196F3;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.save-slot:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
    border-color: #FF6B35;
}

.save-slot.empty {
    background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 100%);
    border-color: #9E9E9E;
    color: #666;
}

.save-slot h4 {
    font-size: 1.3em;
    margin-bottom: 10px;
    color: #1976D2;
}

.save-slot.empty h4 {
    color: #666;
}

.save-slot-info {
    font-size: 0.9em;
    color: #555;
}

.save-slot-date {
    font-size: 0.8em;
    color: #888;
    margin-top: 5px;
}

/* Game Container */
.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #87CEEB;
}

.game-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
}

.game-canvas:active {
    cursor: grabbing;
}

/* Level Up Animation */
@keyframes levelUpPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Floating Currency Text */
.floating-currency-text {
    animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
    0% {
        transform: translate(-50%, -50%) translateY(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translateY(-100px);
        opacity: 0;
    }
}

/* Notification Animations */
@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Collect Animation */
@keyframes collectPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}
