/**
 * Springfield Town Builder - Camera Controller
 * Handles camera movement, zoom, and rotation for town viewing
 */

class CameraController extends EventEmitter {
    constructor(camera, canvas) {
        super();
        this.camera = camera;
        this.canvas = canvas;
        
        // Camera control settings
        this.isEnabled = true;
        this.isDragging = false;
        this.isRotating = false;
        
        // Movement settings optimized for top-down view
        this.panSpeed = 0.8;
        this.zoomSpeed = 0.15;
        this.rotateSpeed = 0.003;
        
        // Limits for top-down view
        this.minDistance = 15;
        this.maxDistance = 100;
        this.minPolarAngle = 0.1;
        this.maxPolarAngle = Math.PI / 3; // More restricted for top-down feel
        
        // Current state
        this.target = new THREE.Vector3(0, 0, 0);
        this.spherical = new THREE.Spherical();
        this.sphericalDelta = new THREE.Spherical();
        
        // Mouse state
        this.mouseButtons = {
            LEFT: THREE.MOUSE.ROTATE,
            MIDDLE: THREE.MOUSE.DOLLY,
            RIGHT: THREE.MOUSE.PAN
        };
        
        this.mouse = {
            current: new THREE.Vector2(),
            previous: new THREE.Vector2(),
            delta: new THREE.Vector2()
        };
        
        // Touch state for mobile support
        this.touches = {
            ONE: THREE.TOUCH.ROTATE,
            TWO: THREE.TOUCH.DOLLY_PAN
        };
        
        this.init();
    }
    
    init() {
        // Set initial camera position in spherical coordinates for top-down view
        this.spherical.setFromVector3(this.camera.position.clone().sub(this.target));

        this.setupEventListeners();
        this.update();

        console.log('Camera Controller initialized');
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.onMouseWheel(e));
        
        // Touch events for mobile
        this.canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));
        
        // Keyboard events for camera movement
        window.addEventListener('keydown', (e) => this.onKeyDown(e));
        
        // Prevent context menu on right click
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    onMouseDown(event) {
        if (!this.isEnabled) return;
        
        event.preventDefault();
        
        this.mouse.previous.set(event.clientX, event.clientY);
        
        switch (event.button) {
            case 0: // Left button - rotate
                this.isRotating = true;
                break;
            case 1: // Middle button - zoom
                break;
            case 2: // Right button - pan
                this.isDragging = true;
                break;
        }
        
        this.canvas.style.cursor = this.isDragging ? 'grabbing' : 'grabbing';
    }
    
    onMouseMove(event) {
        if (!this.isEnabled) return;
        
        this.mouse.current.set(event.clientX, event.clientY);
        this.mouse.delta.subVectors(this.mouse.current, this.mouse.previous);
        
        if (this.isRotating) {
            this.rotateCamera(this.mouse.delta);
        } else if (this.isDragging) {
            this.panCamera(this.mouse.delta);
        }
        
        this.mouse.previous.copy(this.mouse.current);
    }
    
    onMouseUp(event) {
        if (!this.isEnabled) return;
        
        this.isDragging = false;
        this.isRotating = false;
        this.canvas.style.cursor = 'grab';
    }
    
    onMouseWheel(event) {
        if (!this.isEnabled) return;
        
        event.preventDefault();
        
        const delta = event.deltaY > 0 ? 1 : -1;
        this.zoomCamera(delta);
    }
    
    onKeyDown(event) {
        if (!this.isEnabled) return;

        const moveDistance = 3; // Increased for top-down view

        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                // Move forward (up on screen)
                this.panCamera(new THREE.Vector2(0, moveDistance * 15));
                break;
            case 'KeyS':
            case 'ArrowDown':
                // Move backward (down on screen)
                this.panCamera(new THREE.Vector2(0, -moveDistance * 15));
                break;
            case 'KeyA':
            case 'ArrowLeft':
                // Move left
                this.panCamera(new THREE.Vector2(moveDistance * 15, 0));
                break;
            case 'KeyD':
            case 'ArrowRight':
                // Move right
                this.panCamera(new THREE.Vector2(-moveDistance * 15, 0));
                break;
            case 'KeyQ':
                this.zoomCamera(1);
                break;
            case 'KeyE':
                this.zoomCamera(-1);
                break;
        }
    }
    
    onTouchStart(event) {
        if (!this.isEnabled) return;
        
        event.preventDefault();
        
        // Handle touch events for mobile support
        // Implementation would go here for mobile touch controls
    }
    
    onTouchMove(event) {
        if (!this.isEnabled) return;
        event.preventDefault();
    }
    
    onTouchEnd(event) {
        if (!this.isEnabled) return;
        event.preventDefault();
    }
    
    rotateCamera(delta) {
        const element = this.canvas;
        
        this.sphericalDelta.theta -= 2 * Math.PI * delta.x / element.clientHeight * this.rotateSpeed;
        this.sphericalDelta.phi -= 2 * Math.PI * delta.y / element.clientHeight * this.rotateSpeed;
    }
    
    panCamera(delta) {
        const element = this.canvas;
        const targetDistance = this.camera.position.distanceTo(this.target);
        
        // Calculate pan vectors
        const panLeft = new THREE.Vector3();
        const panUp = new THREE.Vector3();
        
        // Get camera's local axes
        panLeft.setFromMatrixColumn(this.camera.matrix, 0); // get X column
        panUp.setFromMatrixColumn(this.camera.matrix, 1); // get Y column
        
        // Scale by distance and screen size
        const scale = targetDistance * this.panSpeed / element.clientHeight;
        
        panLeft.multiplyScalar(-delta.x * scale);
        panUp.multiplyScalar(delta.y * scale);
        
        // Apply pan to target
        this.target.add(panLeft);
        this.target.add(panUp);
    }
    
    zoomCamera(delta) {
        this.spherical.radius *= Math.pow(0.95, this.zoomSpeed * delta);
        
        // Clamp zoom distance
        this.spherical.radius = Math.max(this.minDistance, 
            Math.min(this.maxDistance, this.spherical.radius));
    }
    
    update() {
        // Apply spherical delta
        this.spherical.theta += this.sphericalDelta.theta;
        this.spherical.phi += this.sphericalDelta.phi;
        
        // Clamp polar angle
        this.spherical.phi = Math.max(this.minPolarAngle, 
            Math.min(this.maxPolarAngle, this.spherical.phi));
        
        // Update camera position
        const position = new THREE.Vector3();
        position.setFromSpherical(this.spherical);
        position.add(this.target);
        
        this.camera.position.copy(position);
        this.camera.lookAt(this.target);
        
        // Reset deltas
        this.sphericalDelta.set(0, 0, 0);
        
        return true;
    }
    
    // Public methods for programmatic camera control
    setTarget(x, y, z) {
        if (x instanceof THREE.Vector3) {
            this.target.copy(x);
        } else {
            this.target.set(x, y, z);
        }
    }
    
    getTarget() {
        return this.target.clone();
    }
    
    focusOn(object) {
        if (object.position) {
            this.setTarget(object.position);
        }
    }
    
    reset() {
        this.target.set(0, 0, 0);
        // Set to top-down view: distance=50, polar angle=small (nearly overhead), azimuth=0
        this.spherical.set(50, Math.PI / 6, 0);
        this.update();
    }
    
    enable() {
        this.isEnabled = true;
    }
    
    disable() {
        this.isEnabled = false;
        this.isDragging = false;
        this.isRotating = false;
    }
    
    dispose() {
        // Remove event listeners
        this.canvas.removeEventListener('mousedown', this.onMouseDown);
        this.canvas.removeEventListener('mousemove', this.onMouseMove);
        this.canvas.removeEventListener('mouseup', this.onMouseUp);
        this.canvas.removeEventListener('wheel', this.onMouseWheel);
        this.canvas.removeEventListener('touchstart', this.onTouchStart);
        this.canvas.removeEventListener('touchmove', this.onTouchMove);
        this.canvas.removeEventListener('touchend', this.onTouchEnd);
        window.removeEventListener('keydown', this.onKeyDown);
    }
}
