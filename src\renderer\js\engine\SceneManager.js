/**
 * Springfield Town Builder - Scene Manager
 * Manages 3D scene objects and organization
 */

class SceneManager extends EventEmitter {
    constructor(engine3D) {
        super();
        this.engine3D = engine3D;
        this.scene = engine3D.getScene();
        
        // Scene organization
        this.layers = {
            terrain: new THREE.Group(),
            buildings: new THREE.Group(),
            characters: new THREE.Group(),
            effects: new THREE.Group(),
            ui3d: new THREE.Group()
        };
        
        this.init();
    }
    
    init() {
        // Add layers to scene
        Object.values(this.layers).forEach(layer => {
            this.scene.add(layer);
        });
        
        console.log('Scene Manager initialized');
    }
    
    addToLayer(object, layerName) {
        if (this.layers[layerName]) {
            this.layers[layerName].add(object);
        } else {
            console.warn('Unknown layer:', layerName);
        }
    }
    
    removeFromLayer(object, layerName) {
        if (this.layers[layerName]) {
            this.layers[layerName].remove(object);
        }
    }
    
    getLayer(layerName) {
        return this.layers[layerName];
    }
    
    clearLayer(layerName) {
        if (this.layers[layerName]) {
            while (this.layers[layerName].children.length > 0) {
                this.layers[layerName].remove(this.layers[layerName].children[0]);
            }
        }
    }
    
    dispose() {
        Object.values(this.layers).forEach(layer => {
            this.scene.remove(layer);
        });
        this.removeAllListeners();
    }
}
