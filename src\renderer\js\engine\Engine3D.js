/**
 * Springfield Town Builder - 3D Engine
 * Handles Three.js setup and core 3D functionality
 */

class Engine3D extends EventEmitter {
    constructor(canvas) {
        super();
        this.canvas = canvas;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        this.isRunning = false;
        
        // Raycasting for object selection
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        // Object groups
        this.buildings = new THREE.Group();
        this.characters = new THREE.Group();
        this.terrain = new THREE.Group();
        this.decorations = new THREE.Group();
        
        this.init();
    }
    
    init() {
        this.setupRenderer();
        this.setupScene();
        this.setupCamera();
        this.setupLighting();
        this.setupTerrain();
        this.setupEventListeners();
        
        console.log('3D Engine initialized');
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: true
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Add fog for depth
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        
        // Add object groups to scene
        this.scene.add(this.buildings);
        this.scene.add(this.characters);
        this.scene.add(this.terrain);
        this.scene.add(this.decorations);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            45,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );

        // Position camera for top-down view
        this.camera.position.set(0, 50, 0);
        this.camera.lookAt(0, 0, 0);

        // Slightly angle the camera for better depth perception
        this.camera.position.set(0, 45, 5);
        this.camera.lookAt(0, 0, 0);
    }
    
    setupLighting() {
        // Higher ambient light for top-down view clarity
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
        this.scene.add(ambientLight);

        // Directional light from above for top-down view
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(0, 100, 10);
        directionalLight.castShadow = true;

        // Configure shadow properties for top-down view
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -60;
        directionalLight.shadow.camera.right = 60;
        directionalLight.shadow.camera.top = 60;
        directionalLight.shadow.camera.bottom = -60;

        this.scene.add(directionalLight);

        // Add subtle fill light for better visibility
        const fillLight = new THREE.DirectionalLight(0xffa500, 0.2);
        fillLight.position.set(20, 50, -20);
        this.scene.add(fillLight);
    }
    
    setupTerrain() {
        // Create ground plane
        const groundGeometry = new THREE.PlaneGeometry(100, 100, 20, 20);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x90EE90,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        ground.name = 'ground';
        
        this.terrain.add(ground);
        
        // Add some basic terrain features
        this.addTerrainFeatures();
    }
    
    addTerrainFeatures() {
        // Add some roads
        const roadGeometry = new THREE.PlaneGeometry(2, 50);
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x404040 });
        
        // Main road (horizontal)
        const mainRoad = new THREE.Mesh(roadGeometry, roadMaterial);
        mainRoad.rotation.x = -Math.PI / 2;
        mainRoad.position.y = 0.01;
        this.terrain.add(mainRoad);
        
        // Cross road (vertical)
        const crossRoad = new THREE.Mesh(roadGeometry, roadMaterial);
        crossRoad.rotation.x = -Math.PI / 2;
        crossRoad.rotation.z = Math.PI / 2;
        crossRoad.position.y = 0.01;
        this.terrain.add(crossRoad);
    }
    
    setupEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Handle mouse events for object interaction
        this.canvas.addEventListener('click', (event) => this.onMouseClick(event));
        this.canvas.addEventListener('mousemove', (event) => this.onMouseMove(event));
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onMouseClick(event) {
        this.updateMousePosition(event);
        
        // Perform raycasting
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // Check for intersections with buildings and characters
        const buildingIntersects = this.raycaster.intersectObjects(this.buildings.children, true);
        const characterIntersects = this.raycaster.intersectObjects(this.characters.children, true);
        
        if (buildingIntersects.length > 0) {
            const building = this.findTopLevelObject(buildingIntersects[0].object);
            this.emit('buildingClicked', building);
        } else if (characterIntersects.length > 0) {
            const character = this.findTopLevelObject(characterIntersects[0].object);
            this.emit('characterClicked', character);
        } else {
            // Check ground for building placement
            const groundIntersects = this.raycaster.intersectObjects(this.terrain.children);
            if (groundIntersects.length > 0) {
                const position = groundIntersects[0].point;
                this.emit('groundClicked', position);
            }
        }
    }
    
    onMouseMove(event) {
        this.updateMousePosition(event);
        
        // Handle hover effects
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects([
            ...this.buildings.children,
            ...this.characters.children
        ], true);
        
        // Reset all hover states
        this.clearHoverEffects();
        
        if (intersects.length > 0) {
            const object = this.findTopLevelObject(intersects[0].object);
            this.setHoverEffect(object);
            this.canvas.style.cursor = 'pointer';
        } else {
            this.canvas.style.cursor = 'grab';
        }
    }
    
    updateMousePosition(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }
    
    findTopLevelObject(object) {
        // Find the top-level object in the hierarchy
        while (object.parent && 
               object.parent !== this.buildings && 
               object.parent !== this.characters &&
               object.parent !== this.decorations) {
            object = object.parent;
        }
        return object;
    }
    
    clearHoverEffects() {
        // Remove hover effects from all objects
        this.buildings.children.forEach(building => {
            if (building.userData.originalMaterial) {
                building.traverse(child => {
                    if (child.isMesh) {
                        child.material = building.userData.originalMaterial;
                    }
                });
            }
        });
    }
    
    setHoverEffect(object) {
        // Add hover effect to object
        if (!object.userData.originalMaterial) {
            object.traverse(child => {
                if (child.isMesh) {
                    object.userData.originalMaterial = child.material;
                }
            });
        }
        
        object.traverse(child => {
            if (child.isMesh) {
                child.material = child.material.clone();
                child.material.emissive.setHex(0x444444);
            }
        });
    }
    
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.animate();
        }
    }
    
    stop() {
        this.isRunning = false;
    }
    
    animate() {
        if (!this.isRunning) return;
        
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update animations and systems
        this.emit('update', deltaTime);
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
    
    // Utility methods for adding objects
    addBuilding(building) {
        this.buildings.add(building);
    }
    
    removeBuilding(building) {
        this.buildings.remove(building);
    }
    
    addCharacter(character) {
        this.characters.add(character);
    }
    
    removeCharacter(character) {
        this.characters.remove(character);
    }
    
    addDecoration(decoration) {
        this.decorations.add(decoration);
    }
    
    removeDecoration(decoration) {
        this.decorations.remove(decoration);
    }
    
    getCamera() {
        return this.camera;
    }
    
    getScene() {
        return this.scene;
    }
    
    getRenderer() {
        return this.renderer;
    }
}
