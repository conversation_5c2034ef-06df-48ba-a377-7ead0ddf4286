{"name": "springfield-town-builder", "version": "1.0.0", "description": "A comprehensive Springfield Town Builder desktop game inspired by The Simpsons: Tapped Out", "main": "src/main/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev-server\" \"wait-on http://localhost:3000 && electron .\"", "dev-server": "node src/server/dev-server.js", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build", "pack": "electron-builder --dir", "test": "jest"}, "keywords": ["simpsons", "town-builder", "game", "desktop", "electron"], "author": "Springfield Game Studio", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "jest": "^29.7.0", "wait-on": "^7.2.0"}, "dependencies": {"cannon-es": "^0.20.0", "express": "^4.18.2", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "three": "^0.160.1", "uuid": "^9.0.1"}, "build": {"appId": "com.springfield.townbuilder", "productName": "Springfield Town Builder", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "data/**/*", "node_modules/**/*"], "win": {"target": "nsis"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}