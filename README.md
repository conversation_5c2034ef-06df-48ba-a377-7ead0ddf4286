# Springfield Town Builder

A comprehensive 3D desktop town building game inspired by The Simpsons: Tapped Out. Build your own version of Springfield with iconic buildings, animated characters, and authentic Simpsons references.

## Features

### 🏠 Town Building
- **Interactive 3D Building System**: Place buildings with fun, non-grid-based mechanics
- **Iconic Springfield Buildings**: 
  - Simpson House
  - Kwik-E-Mart (<PERSON><PERSON>'s convenience store)
  - Moe's Tavern
  - Springfield Elementary School
  - Nuclear Power Plant
  - Krusty Burger
  - The Android's Dungeon (Comic Book Guy's store)
  - And many more!

### 👨‍👩‍👧‍👦 Animated Characters
- **Simpson Family**: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>
- **Townspeople**: <PERSON><PERSON>, <PERSON>, <PERSON> Book Guy, and other Springfield residents
- **Character AI**: Characters walk around town and visually perform tasks
- **Authentic Personalities**: Each character behaves according to their show personality

### 🍩 Currency System
- **Money**: Earn from buildings and tasks
- **Donuts**: Premium currency (represented with donut emoji 🍩)
- **XP**: Experience points for leveling up
- **Level System**: Unlock new content as you progress

### 💾 Save System
- **5 Save Slots**: No login required - manage multiple towns
- **Auto-Save**: Automatic progress saving
- **Quick Save**: Manual save functionality

### 🎮 Game Features
- **Task System**: Assign characters to various activities
- **Building Upgrades**: Improve your buildings for better income
- **Income Generation**: Buildings generate money over time
- **3D Graphics**: Full 3D environment with camera controls

### 🌐 Multiplayer Features (Coming Soon)
- **Friends System**: Connect with other players
- **Real-time Rooms**: Collaborative building sessions
- **Trading System**: Exchange resources and items
- **Competitions**: Building contests and events
- **Live Broadcasting**: Share your game actions with friends

## Installation & Running

### Quick Start (Browser Version)
1. Open `test.html` in your web browser
2. The game will load and run directly in the browser
3. All features work except file-based save/load (uses localStorage instead)

### Development Mode
```bash
# Install dependencies
npm install

# Run Electron directly
npm start

# Or use the provided scripts
run-game.bat        # Windows Batch file
run-game.ps1        # PowerShell script
```

### Building for Windows
```bash
# Build Windows executable with installer
npm run build-win

# The installer will be created in the dist/ folder
```

**Note**: If you encounter build issues, you can run the game directly using:
- Browser: Open `test.html`
- Electron: Run `npm start` or use the provided batch/PowerShell scripts

## Game Controls

### Camera Controls
- **Mouse Drag**: Rotate camera around town
- **Mouse Wheel**: Zoom in/out
- **WASD/Arrow Keys**: Pan camera
- **Q/E**: Zoom in/out with keyboard

### Building
1. Click a building category button (bottom left)
2. Select a building from the menu
3. Click on the ground to place it
4. Right-click to cancel placement

### Character Interaction
- **Click Characters**: View character info and assign tasks
- **Click Buildings**: View building info and upgrade options

## UI Layout

- **Top Bar**: Currency display (Money 💰, Donuts 🍩, XP ⭐) and town info
- **Bottom Left**: Building menu with category buttons
- **Bottom Right**: Task panel showing active character tasks
- **Center**: 3D game world

## Technical Details

### Built With
- **Electron**: Cross-platform desktop app framework
- **Three.js**: 3D graphics and rendering
- **Node.js**: Backend functionality
- **HTML5/CSS3/JavaScript**: Frontend interface

### System Requirements
- **OS**: Windows 10 or later
- **RAM**: 4GB minimum, 8GB recommended
- **Graphics**: DirectX 11 compatible
- **Storage**: 500MB available space

## Game Progression

### Starting Out
1. Begin with the Simpson House and Homer
2. Build Kwik-E-Mart to unlock Apu
3. Expand with Moe's Tavern and other buildings
4. Assign tasks to characters to earn money and XP
5. Level up to unlock new buildings and characters

### Advanced Features
- Upgrade buildings for better income
- Collect from buildings regularly
- Complete character tasks for rewards
- Unlock premium buildings with donuts

## Authentic Simpsons References

The game includes authentic references from The Simpsons TV show:
- **Character Tasks**: Homer drinks beer at Moe's, Bart skateboards, Lisa plays saxophone
- **Building Designs**: Faithful recreations of iconic Springfield locations
- **Character Personalities**: Each character behaves true to their show personality
- **Springfield Atmosphere**: Captures the humor and charm of the animated series

## Development Status

### ✅ Completed Features
- 3D engine and camera controls
- Save/load system with multiple slots
- Currency system (money, donuts, XP)
- Building placement and management
- Character system with AI behavior
- Task assignment and completion
- UI framework and menus
- Authentic Simpsons content

### 🚧 In Development
- 3D models for buildings and characters
- Sound effects and music
- More buildings and characters
- Quest system
- Multiplayer features

### 📋 Planned Features
- Seasonal events
- Mini-games
- Character customization
- Town decorations
- Achievement system

## Contributing

This is a fan project inspired by The Simpsons: Tapped Out. All Simpsons characters and references are property of Fox Broadcasting Company.

## License

MIT License - See LICENSE file for details.

---

**D'oh!** Enjoy building your own Springfield! 🍩
